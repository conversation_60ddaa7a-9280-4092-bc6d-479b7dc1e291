# PDF Form Generator

A Python application that generates editable PDF forms with various field types and professional layouts. This project was created to recreate the functionality of the `dr-admin-form.pdf` with fully editable and customizable form fields.

## Features

- **Multiple Field Types**: Text inputs, checkboxes, radio buttons, and dropdown menus
- **Professional Layout**: Clean, organized form sections with proper spacing
- **Customizable Forms**: Create custom forms using configuration dictionaries
- **Validation Support**: Required field indicators and tooltips
- **Cross-platform**: Works on Windows, macOS, and Linux
- **PDF Standards Compliant**: Generated forms work with all major PDF viewers

## Installation

1. **Clone or download this repository**
2. **Install required dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

### Generate Sample Forms
```bash
python3 pdf_form_generator.py --sample
```
This creates two sample forms:
- `admin_form.pdf` - A comprehensive administrative form
- `employee_form.pdf` - A custom employee registration form

### Create a Custom Form
```bash
python3 pdf_form_generator.py my_custom_form.pdf
```

### Get Help
```bash
python3 pdf_form_generator.py --help
```

## Form Field Types

### 1. Text Input Fields
- Single-line text entry
- Customizable width and positioning
- Required field indicators (*)
- Tooltips for user guidance

### 2. Checkboxes
- Boolean selection fields
- Professional styling with labels
- Multiple checkboxes per section

### 3. Radio Button Groups
- Single selection from multiple options
- Visual radio button appearance
- Text field for selected value entry

### 4. Dropdown Menus
- Selection from predefined options
- Options displayed as helper text
- Fallback text input functionality

### 5. Multi-line Text Areas
- Large text input areas
- Comments and description fields
- Adjustable height and width

## Form Sections

The standard administrative form includes:

1. **Personal Information**
   - First Name, Last Name (required)
   - ID Number, Date of Birth (required)
   - Gender selection, Nationality

2. **Contact Information**
   - Street Address, City, State/Province
   - Postal Code, Phone Number (required)
   - Email Address (required)

3. **Employment Information**
   - Employment Status dropdown
   - Job Title, Company Name
   - Work Phone, Annual Income

4. **Additional Information**
   - Emergency contact details
   - Notification preferences
   - Terms agreement checkbox
   - Comments section

5. **Signature Section**
   - Date field (required)
   - Signature field

## Customization

### Creating Custom Forms

You can create custom forms by defining a configuration dictionary:

```python
from pdf_form_generator import CustomFormGenerator

custom_config = {
    'title': 'MY CUSTOM FORM',
    'sections': [
        {
            'title': 'BASIC INFO',
            'fields': [
                {
                    'type': 'text',
                    'name': 'full_name',
                    'label': 'Full Name',
                    'required': True,
                    'width': 300
                },
                {
                    'type': 'dropdown',
                    'name': 'department',
                    'label': 'Department',
                    'options': ['HR', 'IT', 'Finance', 'Marketing']
                },
                {
                    'type': 'checkbox',
                    'name': 'newsletter',
                    'label': 'Subscribe to newsletter'
                }
            ]
        }
    ]
}

generator = CustomFormGenerator("my_form.pdf", custom_config)
generator.create_custom_form()
```

### Field Configuration Options

#### Text Fields
```python
{
    'type': 'text',
    'name': 'field_name',        # Unique field identifier
    'label': 'Field Label',      # Display label
    'required': True,            # Show required indicator
    'width': 200                 # Field width in points
}
```

#### Checkboxes
```python
{
    'type': 'checkbox',
    'name': 'field_name',
    'label': 'Checkbox Label'
}
```

#### Radio Button Groups
```python
{
    'type': 'radio',
    'name': 'field_name',
    'label': 'Selection Label',
    'options': ['Option 1', 'Option 2', 'Option 3']
}
```

#### Dropdown Menus
```python
{
    'type': 'dropdown',
    'name': 'field_name',
    'label': 'Selection Label',
    'options': ['Choice 1', 'Choice 2', 'Choice 3'],
    'width': 250
}
```

## Technical Details

### Dependencies
- **reportlab**: PDF generation and form field creation
- **PyPDF2/pypdf**: PDF analysis and manipulation
- **pdfplumber**: PDF content extraction
- **Pillow**: Image processing support
- **pdf2image**: PDF to image conversion

### PDF Specifications
- **Page Size**: A4 (595.2 x 841.92 points)
- **Margins**: 50 points on all sides
- **Font**: Helvetica family (standard PDF font)
- **Form Fields**: AcroForm compatible
- **Compatibility**: PDF 1.4+ standard

### Layout System
- **Grid-based positioning**: Consistent field alignment
- **Responsive spacing**: Automatic vertical positioning
- **Professional styling**: Clean borders and typography
- **Accessibility**: Tooltips and clear labeling

## File Structure

```
Application-Form/
├── pdf_form_generator.py    # Main form generator class
├── pdf_analyzer.py          # PDF analysis utilities
├── requirements.txt         # Python dependencies
├── README.md               # This documentation
├── dr-admin-form.pdf       # Original form (reference)
├── admin_form.pdf          # Generated administrative form
└── employee_form.pdf       # Generated employee form
```

## Usage Examples

### Basic Usage
```python
from pdf_form_generator import PDFFormGenerator

# Create a standard administrative form
generator = PDFFormGenerator("my_admin_form.pdf")
generator.create_form()
```

### Advanced Customization
```python
from pdf_form_generator import CustomFormGenerator

# Define custom form structure
config = {
    'title': 'REGISTRATION FORM',
    'sections': [
        {
            'title': 'PERSONAL DETAILS',
            'fields': [
                {'type': 'text', 'name': 'name', 'label': 'Full Name', 'required': True},
                {'type': 'text', 'name': 'email', 'label': 'Email', 'required': True},
                {'type': 'radio', 'name': 'type', 'label': 'Type', 'options': ['Student', 'Professional']}
            ]
        }
    ]
}

# Generate the form
generator = CustomFormGenerator("registration.pdf", config)
generator.create_custom_form()
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   pip install --upgrade reportlab pypdf pdfplumber
   ```

2. **Permission Errors**
   - Ensure write permissions in the output directory
   - Close any open PDF files before regenerating

3. **Font Issues**
   - The generator uses standard PDF fonts (Helvetica)
   - No additional font installation required

4. **Field Positioning**
   - Adjust margin and spacing values in the class constructor
   - Modify field_width and field_height for different layouts

### Performance Tips
- Large forms (>50 fields) may take a few seconds to generate
- Use appropriate field widths to prevent overlap
- Test forms in multiple PDF viewers for compatibility

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with various PDF viewers
5. Submit a pull request

## License

This project is open source. Feel free to use, modify, and distribute according to your needs.

## Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Review the examples in the code
3. Test with the sample forms first
4. Ensure all dependencies are properly installed

## Changelog

### Version 1.0
- Initial release
- Basic form field types (text, checkbox, radio, dropdown)
- Standard administrative form template
- Custom form configuration support
- Professional PDF styling
- Cross-platform compatibility