#!/usr/bin/env python3
"""
Example Usage of PDF Form Generator
Demonstrates various ways to create and customize PDF forms.
"""

from pdf_form_generator import PDFFormGenerator, CustomFormGenerator

def create_basic_form():
    """Create a basic administrative form"""
    print("Creating basic administrative form...")

    generator = PDFFormGenerator("basic_admin_form.pdf")
    generator.create_form()

    print("✓ Basic form created: basic_admin_form.pdf")

def create_job_application_form():
    """Create a custom job application form"""
    print("Creating job application form...")

    config = {
        'title': 'JOB APPLICATION FORM',
        'sections': [
            {
                'title': 'APPLICANT INFORMATION',
                'fields': [
                    {'type': 'text', 'name': 'first_name', 'label': 'First Name', 'required': True},
                    {'type': 'text', 'name': 'last_name', 'label': 'Last Name', 'required': True},
                    {'type': 'text', 'name': 'email', 'label': 'Email Address', 'required': True, 'width': 300},
                    {'type': 'text', 'name': 'phone', 'label': 'Phone Number', 'required': True},
                    {'type': 'text', 'name': 'address', 'label': 'Home Address', 'width': 350}
                ]
            },
            {
                'title': 'POSITION DETAILS',
                'fields': [
                    {'type': 'text', 'name': 'position', 'label': 'Position Applied For', 'required': True, 'width': 300},
                    {'type': 'dropdown', 'name': 'department', 'label': 'Preferred Department',
                     'options': ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations']},
                    {'type': 'radio', 'name': 'employment_type', 'label': 'Employment Type',
                     'options': ['Full-time', 'Part-time', 'Contract', 'Internship']},
                    {'type': 'text', 'name': 'salary_expectation', 'label': 'Salary Expectation'},
                    {'type': 'text', 'name': 'start_date', 'label': 'Available Start Date'}
                ]
            },
            {
                'title': 'EXPERIENCE & QUALIFICATIONS',
                'fields': [
                    {'type': 'text', 'name': 'education', 'label': 'Highest Education', 'width': 300},
                    {'type': 'text', 'name': 'experience_years', 'label': 'Years of Experience'},
                    {'type': 'text', 'name': 'previous_employer', 'label': 'Previous Employer', 'width': 300},
                    {'type': 'text', 'name': 'skills', 'label': 'Key Skills', 'width': 350}
                ]
            },
            {
                'title': 'ADDITIONAL INFORMATION',
                'fields': [
                    {'type': 'checkbox', 'name': 'willing_relocate', 'label': 'Willing to relocate'},
                    {'type': 'checkbox', 'name': 'willing_travel', 'label': 'Willing to travel'},
                    {'type': 'checkbox', 'name': 'background_check', 'label': 'Consent to background check'},
                    {'type': 'text', 'name': 'references', 'label': 'References', 'width': 350}
                ]
            }
        ]
    }

    generator = CustomFormGenerator("job_application_form.pdf", config)
    generator.create_custom_form()

    print("✓ Job application form created: job_application_form.pdf")

def create_survey_form():
    """Create a customer survey form"""
    print("Creating customer survey form...")

    config = {
        'title': 'CUSTOMER SATISFACTION SURVEY',
        'sections': [
            {
                'title': 'CUSTOMER INFORMATION',
                'fields': [
                    {'type': 'text', 'name': 'customer_name', 'label': 'Name (Optional)', 'width': 250},
                    {'type': 'text', 'name': 'customer_email', 'label': 'Email (Optional)', 'width': 250},
                    {'type': 'dropdown', 'name': 'customer_type', 'label': 'Customer Type',
                     'options': ['New Customer', 'Returning Customer', 'Premium Member']}
                ]
            },
            {
                'title': 'SERVICE EVALUATION',
                'fields': [
                    {'type': 'radio', 'name': 'overall_satisfaction', 'label': 'Overall Satisfaction',
                     'options': ['Excellent', 'Good', 'Fair', 'Poor']},
                    {'type': 'radio', 'name': 'service_quality', 'label': 'Service Quality',
                     'options': ['Excellent', 'Good', 'Fair', 'Poor']},
                    {'type': 'radio', 'name': 'response_time', 'label': 'Response Time',
                     'options': ['Very Fast', 'Fast', 'Average', 'Slow']},
                    {'type': 'checkbox', 'name': 'recommend', 'label': 'Would recommend to others'}
                ]
            },
            {
                'title': 'FEEDBACK',
                'fields': [
                    {'type': 'text', 'name': 'improvements', 'label': 'Suggested Improvements', 'width': 400},
                    {'type': 'text', 'name': 'additional_comments', 'label': 'Additional Comments', 'width': 400}
                ]
            }
        ]
    }

    generator = CustomFormGenerator("customer_survey_form.pdf", config)
    generator.create_custom_form()

    print("✓ Customer survey form created: customer_survey_form.pdf")

def create_registration_form():
    """Create an event registration form"""
    print("Creating event registration form...")

    config = {
        'title': 'EVENT REGISTRATION FORM',
        'sections': [
            {
                'title': 'PARTICIPANT DETAILS',
                'fields': [
                    {'type': 'text', 'name': 'participant_name', 'label': 'Full Name', 'required': True, 'width': 300},
                    {'type': 'text', 'name': 'organization', 'label': 'Organization/Company', 'width': 300},
                    {'type': 'text', 'name': 'job_title', 'label': 'Job Title', 'width': 250},
                    {'type': 'text', 'name': 'email', 'label': 'Email Address', 'required': True, 'width': 300},
                    {'type': 'text', 'name': 'phone', 'label': 'Phone Number', 'required': True}
                ]
            },
            {
                'title': 'EVENT PREFERENCES',
                'fields': [
                    {'type': 'dropdown', 'name': 'session_track', 'label': 'Preferred Track',
                     'options': ['Technology', 'Business', 'Marketing', 'Design', 'General']},
                    {'type': 'radio', 'name': 'attendance_type', 'label': 'Attendance Type',
                     'options': ['In-Person', 'Virtual', 'Hybrid']},
                    {'type': 'checkbox', 'name': 'networking', 'label': 'Interested in networking session'},
                    {'type': 'checkbox', 'name': 'materials', 'label': 'Request digital materials'},
                    {'type': 'text', 'name': 'dietary_requirements', 'label': 'Dietary Requirements', 'width': 300}
                ]
            }
        ]
    }

    generator = CustomFormGenerator("event_registration_form.pdf", config)
    generator.create_custom_form()

    print("✓ Event registration form created: event_registration_form.pdf")

def main():
    """Run all examples"""
    print("PDF Form Generator - Usage Examples")
    print("==================================")

    # Create various types of forms
    create_basic_form()
    print()

    create_job_application_form()
    print()

    create_survey_form()
    print()

    create_registration_form()
    print()

    print("All example forms have been created successfully!")
    print("\nGenerated files:")
    print("- basic_admin_form.pdf")
    print("- job_application_form.pdf")
    print("- customer_survey_form.pdf")
    print("- event_registration_form.pdf")
    print("\nOpen these files in a PDF viewer to see the editable forms.")

if __name__ == "__main__":
    main()