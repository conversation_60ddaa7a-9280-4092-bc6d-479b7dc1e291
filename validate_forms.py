#!/usr/bin/env python3
"""
PDF Form Validation Script
Validates that generated PDF forms are properly structured and contain form fields.
"""

import os
import sys
from pathlib import Path

def validate_pdf_form(pdf_path):
    """Validate a PDF form file"""
    try:
        import pypdf

        if not os.path.exists(pdf_path):
            return False, f"File not found: {pdf_path}"

        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)

            # Check basic PDF structure
            if len(reader.pages) == 0:
                return False, "PDF has no pages"

            # Check for form fields
            form_fields = reader.get_form_text_fields()
            if not form_fields:
                return False, "No form fields detected"

            # Get page info
            page = reader.pages[0]
            page_size = (page.mediabox.width, page.mediabox.height)

            return True, {
                'pages': len(reader.pages),
                'form_fields': len(form_fields),
                'field_names': list(form_fields.keys()),
                'page_size': page_size,
                'file_size': os.path.getsize(pdf_path)
            }

    except Exception as e:
        return False, f"Error validating PDF: {str(e)}"

def main():
    """Main validation function"""
    print("PDF Form Validation")
    print("==================")

    # List of generated PDF files to validate
    pdf_files = [
        "admin_form.pdf",
        "employee_form.pdf",
        "basic_admin_form.pdf",
        "job_application_form.pdf",
        "customer_survey_form.pdf",
        "event_registration_form.pdf"
    ]

    total_files = 0
    valid_files = 0

    for pdf_file in pdf_files:
        if os.path.exists(pdf_file):
            total_files += 1
            print(f"\nValidating: {pdf_file}")
            print("-" * (len(pdf_file) + 12))

            is_valid, result = validate_pdf_form(pdf_file)

            if is_valid:
                valid_files += 1
                print("✓ VALID PDF Form")
                print(f"  Pages: {result['pages']}")
                print(f"  Form Fields: {result['form_fields']}")
                print(f"  Page Size: {result['page_size'][0]} x {result['page_size'][1]} points")
                print(f"  File Size: {result['file_size']:,} bytes")

                if result['form_fields'] > 0:
                    print("  Field Names:")
                    for field_name in result['field_names'][:10]:  # Show first 10 fields
                        print(f"    - {field_name}")
                    if len(result['field_names']) > 10:
                        print(f"    ... and {len(result['field_names']) - 10} more")
            else:
                print(f"✗ INVALID: {result}")
        else:
            print(f"\n⚠ File not found: {pdf_file}")

    print(f"\n{'='*50}")
    print(f"Validation Summary:")
    print(f"Total files checked: {total_files}")
    print(f"Valid forms: {valid_files}")
    print(f"Success rate: {(valid_files/total_files*100) if total_files > 0 else 0:.1f}%")

    if valid_files == total_files and total_files > 0:
        print("\n🎉 All PDF forms are valid and contain editable fields!")
        return 0
    else:
        print(f"\n⚠ {total_files - valid_files} forms failed validation")
        return 1

if __name__ == "__main__":
    sys.exit(main())