#!/usr/bin/env python3
"""
PDF Form Generator
Creates editable PDF forms with various field types and layouts.
Based on analysis of dr-admin-form.pdf structure.
"""

import sys
import os
from pathlib import Path
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.colors import black, white, gray
from reportlab.lib.units import inch, mm
from reportlab.pdfbase import pdfform
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT

class PDFFormGenerator:
    """Main class for generating PDF forms"""

    def __init__(self, output_filename="generated_form.pdf"):
        self.output_filename = output_filename
        self.page_width, self.page_height = A4
        self.margin = 50  # 50 points margin
        self.current_y = self.page_height - self.margin
        self.line_height = 20
        self.field_height = 25
        self.field_width = 200

    def create_form(self):
        """Create the main form structure"""
        # Create canvas
        c = canvas.Canvas(self.output_filename, pagesize=A4)

        # Add title
        self.add_title(c, "ADMINISTRATIVE FORM")

        # Add form sections
        self.add_personal_info_section(c)
        self.add_contact_info_section(c)
        self.add_employment_section(c)
        self.add_additional_info_section(c)
        self.add_signature_section(c)

        # Save the PDF
        c.save()
        print(f"Form generated successfully: {self.output_filename}")

    def add_title(self, canvas, title):
        """Add form title"""
        canvas.setFont("Helvetica-Bold", 16)
        title_width = canvas.stringWidth(title, "Helvetica-Bold", 16)
        x = (self.page_width - title_width) / 2
        canvas.drawString(x, self.current_y, title)
        self.current_y -= 40

    def add_section_header(self, canvas, header):
        """Add section header"""
        canvas.setFont("Helvetica-Bold", 12)
        canvas.drawString(self.margin, self.current_y, header)
        self.current_y -= 25

    def add_text_field(self, canvas, label, field_name, x=None, y=None, width=None, required=False):
        """Add a text input field"""
        if x is None:
            x = self.margin
        if y is None:
            y = self.current_y
        if width is None:
            width = self.field_width

        # Add label
        canvas.setFont("Helvetica", 10)
        label_text = f"{label}{'*' if required else ''}:"
        canvas.drawString(x, y + 5, label_text)

        # Add field box
        canvas.rect(x + 120, y - 5, width, self.field_height, stroke=1, fill=0)

        # Add form field using pdfform
        canvas.acroForm.textfield(
            name=field_name,
            tooltip=f"Enter {label.lower()}",
            x=x + 120,
            y=y - 5,
            borderStyle='inset',
            width=width,
            height=self.field_height,
            textColor=black,
            fillColor=white,
            fontSize=10
        )

        if x == self.margin:  # Only update y if using default x position
            self.current_y -= 35

    def add_checkbox(self, canvas, label, field_name, x=None, y=None):
        """Add a checkbox field"""
        if x is None:
            x = self.margin
        if y is None:
            y = self.current_y

        # Add checkbox
        canvas.acroForm.checkbox(
            name=field_name,
            tooltip=f"Check {label.lower()}",
            x=x,
            y=y,
            size=15,
            buttonStyle='check',
            borderColor=black,
            fillColor=white,
            textColor=black,
            forceBorder=True
        )

        # Add label
        canvas.setFont("Helvetica", 10)
        canvas.drawString(x + 20, y + 2, label)

        if x == self.margin:  # Only update y if using default x position
            self.current_y -= 25

    def add_radio_group(self, canvas, label, field_name, options, x=None, y=None):
        """Add a radio button group (simplified as checkboxes)"""
        if x is None:
            x = self.margin
        if y is None:
            y = self.current_y

        # Add label
        canvas.setFont("Helvetica", 10)
        canvas.drawString(x, y + 5, f"{label}:")

        # Add checkboxes for each option (simpler than radio buttons)
        current_x = x + 120

        for i, option in enumerate(options):
            # Draw a small circle for radio button appearance
            canvas.circle(current_x + 6, y + 6, 6, stroke=1, fill=0)

            # Add option label
            canvas.setFont("Helvetica", 9)
            canvas.drawString(current_x + 15, y + 2, option)
            current_x += len(option) * 6 + 40  # Adjust spacing based on text length

        # Add a text field for the selected value
        canvas.setFont("Helvetica", 8)
        canvas.drawString(x, y - 15, "Selected:")
        canvas.rect(x + 60, y - 20, 100, 15, stroke=1, fill=0)
        canvas.acroForm.textfield(
            name=field_name,
            tooltip=f"Enter selected {label.lower()}",
            x=x + 60,
            y=y - 20,
            borderStyle='inset',
            width=100,
            height=15,
            textColor=black,
            fillColor=white,
            fontSize=9
        )

        if x == self.margin:  # Only update y if using default x position
            self.current_y -= 45

    def add_dropdown(self, canvas, label, field_name, options, x=None, y=None, width=None):
        """Add a dropdown field (simplified as text field with options listed)"""
        if x is None:
            x = self.margin
        if y is None:
            y = self.current_y
        if width is None:
            width = self.field_width

        # Add label
        canvas.setFont("Helvetica", 10)
        canvas.drawString(x, y + 5, f"{label}:")

        # Add text field for dropdown (user can type or select)
        canvas.rect(x + 120, y - 5, width, self.field_height, stroke=1, fill=0)
        canvas.acroForm.textfield(
            name=field_name,
            tooltip=f"Select or enter {label.lower()}",
            x=x + 120,
            y=y - 5,
            borderStyle='inset',
            width=width,
            height=self.field_height,
            textColor=black,
            fillColor=white,
            fontSize=10
        )

        # Add options as text below the field
        canvas.setFont("Helvetica", 8)
        options_text = f"Options: {', '.join(options)}"
        canvas.drawString(x + 120, y - 20, options_text)

        if x == self.margin:  # Only update y if using default x position
            self.current_y -= 50  # Extra space for options text

    def add_personal_info_section(self, canvas):
        """Add personal information section"""
        self.add_section_header(canvas, "PERSONAL INFORMATION")

        # First row - Name fields
        self.add_text_field(canvas, "First Name", "first_name", required=True)
        self.add_text_field(canvas, "Last Name", "last_name", required=True)

        # Second row - ID and Date of Birth
        self.add_text_field(canvas, "ID Number", "id_number", required=True)
        self.add_text_field(canvas, "Date of Birth", "date_of_birth", required=True)

        # Third row - Gender and Nationality
        self.add_radio_group(canvas, "Gender", "gender", ["Male", "Female", "Other"])
        self.add_text_field(canvas, "Nationality", "nationality")

        self.current_y -= 20  # Extra space between sections

    def add_contact_info_section(self, canvas):
        """Add contact information section"""
        self.add_section_header(canvas, "CONTACT INFORMATION")

        # Address fields
        self.add_text_field(canvas, "Street Address", "street_address", width=300, required=True)
        self.add_text_field(canvas, "City", "city", required=True)
        self.add_text_field(canvas, "State/Province", "state_province")
        self.add_text_field(canvas, "Postal Code", "postal_code", required=True)

        # Contact details
        self.add_text_field(canvas, "Phone Number", "phone_number", required=True)
        self.add_text_field(canvas, "Email Address", "email_address", width=300, required=True)

        self.current_y -= 20  # Extra space between sections

    def add_employment_section(self, canvas):
        """Add employment information section"""
        self.add_section_header(canvas, "EMPLOYMENT INFORMATION")

        # Employment status
        employment_options = ["Employed", "Unemployed", "Self-Employed", "Student", "Retired"]
        self.add_dropdown(canvas, "Employment Status", "employment_status", employment_options)

        # Job details
        self.add_text_field(canvas, "Job Title", "job_title", width=300)
        self.add_text_field(canvas, "Company Name", "company_name", width=300)
        self.add_text_field(canvas, "Work Phone", "work_phone")

        # Income information
        self.add_text_field(canvas, "Annual Income", "annual_income")

        self.current_y -= 20  # Extra space between sections

    def add_additional_info_section(self, canvas):
        """Add additional information section"""
        self.add_section_header(canvas, "ADDITIONAL INFORMATION")

        # Emergency contact
        self.add_text_field(canvas, "Emergency Contact Name", "emergency_contact_name", width=300)
        self.add_text_field(canvas, "Emergency Contact Phone", "emergency_contact_phone")

        # Checkboxes for various options
        self.add_checkbox(canvas, "I agree to receive email notifications", "email_notifications")
        self.add_checkbox(canvas, "I agree to receive SMS notifications", "sms_notifications")
        self.add_checkbox(canvas, "I have read and agree to the terms and conditions", "terms_agreement")

        # Comments field (larger text area)
        canvas.setFont("Helvetica", 10)
        canvas.drawString(self.margin, self.current_y + 5, "Additional Comments:")
        canvas.rect(self.margin + 120, self.current_y - 60, 350, 80, stroke=1, fill=0)

        canvas.acroForm.textfield(
            name="comments",
            tooltip="Enter additional comments",
            x=self.margin + 120,
            y=self.current_y - 60,
            borderStyle='inset',
            width=350,
            height=80,
            textColor=black,
            fillColor=white,
            fontSize=10
        )

        self.current_y -= 100  # Extra space for comments field

    def add_signature_section(self, canvas):
        """Add signature section"""
        self.add_section_header(canvas, "SIGNATURE")

        # Date field
        self.add_text_field(canvas, "Date", "signature_date", width=150, required=True)

        # Signature field
        canvas.setFont("Helvetica", 10)
        canvas.drawString(self.margin, self.current_y + 5, "Signature:")
        canvas.rect(self.margin + 120, self.current_y - 5, 300, 40, stroke=1, fill=0)

        canvas.acroForm.textfield(
            name="signature",
            tooltip="Digital signature or print name",
            x=self.margin + 120,
            y=self.current_y - 5,
            borderStyle='inset',
            width=300,
            height=40,
            textColor=black,
            fillColor=white,
            fontSize=12
        )

class CustomFormGenerator(PDFFormGenerator):
    """Extended form generator with customization options"""

    def __init__(self, output_filename="custom_form.pdf", form_config=None):
        super().__init__(output_filename)
        self.form_config = form_config or {}

    def create_custom_form(self):
        """Create a form based on custom configuration"""
        c = canvas.Canvas(self.output_filename, pagesize=A4)

        # Add custom title
        title = self.form_config.get('title', 'CUSTOM FORM')
        self.add_title(c, title)

        # Add custom sections
        sections = self.form_config.get('sections', [])
        for section in sections:
            self.add_custom_section(c, section)

        c.save()
        print(f"Custom form generated successfully: {self.output_filename}")

    def add_custom_section(self, canvas, section):
        """Add a custom section based on configuration"""
        section_title = section.get('title', 'SECTION')
        self.add_section_header(canvas, section_title)

        fields = section.get('fields', [])
        for field in fields:
            field_type = field.get('type', 'text')
            field_name = field.get('name', 'field')
            field_label = field.get('label', 'Field')
            field_required = field.get('required', False)

            if field_type == 'text':
                width = field.get('width', self.field_width)
                self.add_text_field(canvas, field_label, field_name, width=width, required=field_required)
            elif field_type == 'checkbox':
                self.add_checkbox(canvas, field_label, field_name)
            elif field_type == 'radio':
                options = field.get('options', ['Option 1', 'Option 2'])
                self.add_radio_group(canvas, field_label, field_name, options)
            elif field_type == 'dropdown':
                options = field.get('options', ['Option 1', 'Option 2'])
                width = field.get('width', self.field_width)
                self.add_dropdown(canvas, field_label, field_name, options, width=width)

        self.current_y -= 20  # Extra space between sections

def create_sample_forms():
    """Create sample forms to demonstrate functionality"""

    # Create standard administrative form
    print("Creating standard administrative form...")
    generator = PDFFormGenerator("admin_form.pdf")
    generator.create_form()

    # Create custom form example
    print("Creating custom form example...")
    custom_config = {
        'title': 'EMPLOYEE REGISTRATION FORM',
        'sections': [
            {
                'title': 'BASIC INFORMATION',
                'fields': [
                    {'type': 'text', 'name': 'employee_id', 'label': 'Employee ID', 'required': True},
                    {'type': 'text', 'name': 'full_name', 'label': 'Full Name', 'required': True, 'width': 300},
                    {'type': 'dropdown', 'name': 'department', 'label': 'Department',
                     'options': ['HR', 'IT', 'Finance', 'Marketing', 'Operations']},
                    {'type': 'radio', 'name': 'employment_type', 'label': 'Employment Type',
                     'options': ['Full-time', 'Part-time', 'Contract']}
                ]
            },
            {
                'title': 'PREFERENCES',
                'fields': [
                    {'type': 'checkbox', 'name': 'newsletter', 'label': 'Subscribe to company newsletter'},
                    {'type': 'checkbox', 'name': 'parking', 'label': 'Require parking space'},
                    {'type': 'text', 'name': 'start_date', 'label': 'Preferred Start Date', 'required': True}
                ]
            }
        ]
    }

    custom_generator = CustomFormGenerator("employee_form.pdf", custom_config)
    custom_generator.create_custom_form()

def main():
    """Main function"""
    print("PDF Form Generator")
    print("==================")

    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print_help()
            return 0
        elif sys.argv[1] == "--sample":
            create_sample_forms()
            return 0
        else:
            # Custom output filename
            output_file = sys.argv[1]
            generator = PDFFormGenerator(output_file)
            generator.create_form()
            return 0
    else:
        # Default behavior - create sample forms
        create_sample_forms()
        return 0

def print_help():
    """Print help information"""
    print("""
Usage: python3 pdf_form_generator.py [options] [output_filename]

Options:
  --help, -h     Show this help message
  --sample       Create sample forms (admin_form.pdf and employee_form.pdf)

Arguments:
  output_filename    Name of the output PDF file (default: creates sample forms)

Examples:
  python3 pdf_form_generator.py                    # Create sample forms
  python3 pdf_form_generator.py my_form.pdf        # Create form with custom filename
  python3 pdf_form_generator.py --sample           # Create sample forms explicitly

Features:
- Text input fields with validation
- Checkboxes and radio buttons
- Dropdown menus
- Multi-line text areas
- Customizable layouts
- Professional formatting
""")

if __name__ == "__main__":
    sys.exit(main())