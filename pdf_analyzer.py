#!/usr/bin/env python3
"""
PDF Form Analyzer
Analyzes the structure of the dr-admin-form.pdf to understand its layout and form fields.
"""

import sys
import os
from pathlib import Path

def analyze_pdf_with_pdfplumber():
    """Analyze PDF using pdfplumber library"""
    try:
        import pdfplumber

        pdf_path = "dr-admin-form.pdf"
        if not os.path.exists(pdf_path):
            print(f"Error: {pdf_path} not found")
            return None

        print("=== PDF Analysis with pdfplumber ===")

        with pdfplumber.open(pdf_path) as pdf:
            print(f"Number of pages: {len(pdf.pages)}")

            for i, page in enumerate(pdf.pages):
                print(f"\n--- Page {i+1} ---")
                print(f"Page size: {page.width} x {page.height}")

                # Extract text
                text = page.extract_text()
                if text:
                    print("Text content:")
                    print(text[:500] + "..." if len(text) > 500 else text)

                # Extract tables if any
                tables = page.extract_tables()
                if tables:
                    print(f"Found {len(tables)} tables")

                # Get page objects (forms, images, etc.)
                if hasattr(page, 'objects'):
                    print(f"Page objects: {len(page.objects)}")

        return True

    except ImportError:
        print("pdfplumber not available")
        return False
    except Exception as e:
        print(f"Error with pdfplumber: {e}")
        return False

def analyze_pdf_with_pypdf():
    """Analyze PDF using PyPDF2/pypdf library"""
    try:
        import pypdf

        pdf_path = "dr-admin-form.pdf"
        if not os.path.exists(pdf_path):
            print(f"Error: {pdf_path} not found")
            return None

        print("\n=== PDF Analysis with pypdf ===")

        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)

            print(f"Number of pages: {len(reader.pages)}")
            print(f"PDF metadata: {reader.metadata}")

            # Check for form fields
            if reader.get_form_text_fields():
                print("Form fields found:")
                for field_name, field_value in reader.get_form_text_fields().items():
                    print(f"  {field_name}: {field_value}")
            else:
                print("No form fields detected")

            # Analyze each page
            for i, page in enumerate(reader.pages):
                print(f"\n--- Page {i+1} ---")

                # Get page dimensions
                mediabox = page.mediabox
                print(f"Page dimensions: {mediabox.width} x {mediabox.height}")

                # Extract text
                try:
                    text = page.extract_text()
                    if text.strip():
                        print("Text content:")
                        print(text[:500] + "..." if len(text) > 500 else text)
                    else:
                        print("No extractable text found")
                except Exception as e:
                    print(f"Error extracting text: {e}")

        return True

    except ImportError:
        print("pypdf not available")
        return False
    except Exception as e:
        print(f"Error with pypdf: {e}")
        return False

def extract_images_from_pdf():
    """Extract images from PDF for visual analysis"""
    try:
        from pdf2image import convert_from_path

        pdf_path = "dr-admin-form.pdf"
        if not os.path.exists(pdf_path):
            print(f"Error: {pdf_path} not found")
            return False

        print("\n=== Extracting Images from PDF ===")

        # Convert PDF to images
        images = convert_from_path(pdf_path, dpi=200)

        for i, image in enumerate(images):
            image_path = f"form_page_{i+1}.png"
            image.save(image_path, 'PNG')
            print(f"Saved page {i+1} as {image_path}")
            print(f"Image size: {image.size}")

        print(f"Extracted {len(images)} page(s) as images")
        print("You can now visually examine the form layout in the generated PNG files")

        return True

    except ImportError:
        print("pdf2image not available")
        return False
    except Exception as e:
        print(f"Error extracting images: {e}")
        return False

def analyze_form_structure():
    """Analyze what we know about the form structure"""
    print("\n=== Form Structure Analysis ===")
    print("Based on the PDF analysis:")
    print("- Single page form")
    print("- A4 size (595.2 x 841.92 points)")
    print("- Scanned/image-based document (no extractable text)")
    print("- No existing form fields detected")
    print("- Created by RICOH IM C2010 (likely a scanner)")

    print("\nThis appears to be a scanned administrative form.")
    print("We'll need to create form fields based on visual layout.")

def main():
    """Main analysis function"""
    print("PDF Form Analyzer")
    print("================")

    # Try different PDF libraries
    success = False

    # Try pdfplumber first (better for layout analysis)
    if analyze_pdf_with_pdfplumber():
        success = True

    # Try pypdf for form field detection
    if analyze_pdf_with_pypdf():
        success = True

    # Extract images for visual analysis
    if extract_images_from_pdf():
        success = True

    # Analyze what we know
    analyze_form_structure()

    if not success:
        print("\nNo PDF libraries available. Please install:")
        print("pip install pdfplumber pypdf pdf2image")
        return 1

    print("\n=== Analysis Complete ===")
    return 0

if __name__ == "__main__":
    sys.exit(main())